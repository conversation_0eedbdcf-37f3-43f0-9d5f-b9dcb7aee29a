#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触动精灵脚本数据库管理工具
提供数据库查询、统计、导出等功能
"""

import sqlite3
import argparse
from datetime import datetime
from tsp_scraper import TSPScraper

def show_statistics(db_path: str = 'tsp_scripts.db'):
    """显示数据库统计信息"""
    scraper = TSPScraper(db_path)
    stats = scraper.get_statistics()
    
    print("=" * 50)
    print("触动精灵脚本数据库统计信息")
    print("=" * 50)
    print(f"数据库文件: {db_path}")
    print(f"总脚本数量: {stats['total_scripts']}")
    print(f"免费脚本: {stats['free_scripts']}")
    print(f"付费脚本: {stats['paid_scripts']}")
    print(f"上次爬取到ID: {stats['last_crawled_id']}")
    print(f"总处理数量: {stats['total_processed']}")
    print(f"成功收集数量: {stats['total_success']}")
    
    if stats['total_processed'] > 0:
        success_rate = (stats['total_success'] / stats['total_processed']) * 100
        print(f"成功率: {success_rate:.2f}%")

def export_to_markdown(db_path: str = 'tsp_scripts.db', output_file: str = 'tsp_scripts.md'):
    """导出数据库内容为Markdown文件"""
    scraper = TSPScraper(db_path)
    scraper.export_to_markdown(output_file)
    print(f"数据已导出到: {output_file}")

def search_scripts(db_path: str = 'tsp_scripts.db', keyword: str = '', limit: int = 10):
    """搜索脚本"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    if keyword:
        cursor.execute('''
            SELECT script_id, script_name, platform_support, charge_type, developer_name
            FROM scripts 
            WHERE script_name LIKE ? OR script_description LIKE ? OR developer_name LIKE ?
            ORDER BY script_id
            LIMIT ?
        ''', (f'%{keyword}%', f'%{keyword}%', f'%{keyword}%', limit))
    else:
        cursor.execute('''
            SELECT script_id, script_name, platform_support, charge_type, developer_name
            FROM scripts 
            ORDER BY script_id DESC
            LIMIT ?
        ''', (limit,))
    
    results = cursor.fetchall()
    conn.close()
    
    if results:
        print(f"\n搜索结果 (关键词: '{keyword}', 显示前{limit}条):")
        print("-" * 80)
        print(f"{'ID':<8} {'脚本名称':<20} {'平台':<12} {'收费':<8} {'开发者':<15}")
        print("-" * 80)
        
        for row in results:
            script_id, name, platform, charge, developer = row
            name = (name[:17] + '...') if len(name) > 20 else name
            developer = (developer[:12] + '...') if len(developer) > 15 else developer
            print(f"{script_id:<8} {name:<20} {platform:<12} {charge:<8} {developer:<15}")
    else:
        print("未找到匹配的脚本")

def show_script_detail(db_path: str = 'tsp_scripts.db', script_id: int = 0):
    """显示脚本详细信息"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT * FROM scripts WHERE script_id = ?
    ''', (script_id,))
    
    result = cursor.fetchone()
    conn.close()
    
    if result:
        columns = ['script_id', 'script_name', 'platform_support', 'charge_type', 'trial_type', 
                  'script_description', 'developer_contact', 'developer_name', 'created_at', 'updated_at']
        
        print(f"\n脚本详细信息 (ID: {script_id}):")
        print("=" * 50)
        
        for i, column in enumerate(columns):
            value = result[i] if result[i] is not None else 'N/A'
            print(f"{column.replace('_', ' ').title()}: {value}")
    else:
        print(f"未找到脚本ID: {script_id}")

def reset_progress(db_path: str = 'tsp_scripts.db'):
    """重置爬取进度"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('DELETE FROM crawl_progress')
    conn.commit()
    conn.close()
    
    print("爬取进度已重置，下次运行将从头开始")

def main():
    parser = argparse.ArgumentParser(description='触动精灵脚本数据库管理工具')
    parser.add_argument('--db', default='tsp_scripts.db', help='数据库文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 统计命令
    subparsers.add_parser('stats', help='显示数据库统计信息')
    
    # 导出命令
    export_parser = subparsers.add_parser('export', help='导出为Markdown文件')
    export_parser.add_argument('--output', default='tsp_scripts.md', help='输出文件名')
    
    # 搜索命令
    search_parser = subparsers.add_parser('search', help='搜索脚本')
    search_parser.add_argument('keyword', nargs='?', default='', help='搜索关键词')
    search_parser.add_argument('--limit', type=int, default=10, help='显示结果数量')
    
    # 详情命令
    detail_parser = subparsers.add_parser('detail', help='显示脚本详细信息')
    detail_parser.add_argument('script_id', type=int, help='脚本ID')
    
    # 重置命令
    subparsers.add_parser('reset', help='重置爬取进度')
    
    args = parser.parse_args()
    
    if args.command == 'stats':
        show_statistics(args.db)
    elif args.command == 'export':
        export_to_markdown(args.db, args.output)
    elif args.command == 'search':
        search_scripts(args.db, args.keyword, args.limit)
    elif args.command == 'detail':
        show_script_detail(args.db, args.script_id)
    elif args.command == 'reset':
        reset_progress(args.db)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
