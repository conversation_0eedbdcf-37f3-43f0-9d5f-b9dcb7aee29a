<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8"/>
    <meta name="apple-mobile-web-app-title" content="触动开发者" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="renderer" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="robots" content="index, follow" />

    <link rel="apple-touch-icon-precomposed" href="/app-icon.png">
    <link rel="shortcut icon" href="/favicon.ico">

    <meta name="csrf-param" content="_csrf">
    <meta name="csrf-token" content="T0FQdFguUjcfdhQ2Ektnb3YYYjI5axdbByoVHCJnAA4qCDEyMkpkYw==">
    <title>Not Found (#404)</title>
    <meta name="description" content="触动精灵开发者平台" />
    <meta name="HandheldFriendly" content="True" />
    <meta name="MobileOptimized" content="320" />
    <link rel="stylesheet" href="/css/swiper.min.css" />
    <script src="/js/swiper.min.js"></script>
    <link rel="stylesheet" href="//play4u.touchsprite.com/cdn/bootstrap/3.3.5/css/bootstrap.min.css">
    <script src="//play4u.touchsprite.com/cdn/jquery/1.11.3/jquery.min.js"></script>
    <script src="/js/index.js"></script>
    <script src="//play4u.touchsprite.com/cdn/bootstrap/3.3.5/js/bootstrap.min.js"></script>

    <link href="/css/site.css" rel="stylesheet">
<script src="/assets/fd480ddd/yii.js"></script>
    <!--[if lte IE 9]>
        <script src="/js/json2.js"></script>
        <script src="http://apps.bdimg.com/libs/html5shiv/3.7/html5shiv.min.js"></script>
        <script src="http://apps.bdimg.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

<!--     <script src="/js/bootstrap-slider.min.js"></script> -->

</head>
<body>
        <div class="wrap">
<nav id="w0" class="navbar-inverse navbar-fixed-top navbar" role="navigation"><div class="container"><div class="navbar-header"><button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#w0-collapse"><span class="sr-only">Toggle navigation</span>
<span class="icon-bar"></span>
<span class="icon-bar"></span>
<span class="icon-bar"></span></button><a class="navbar-brand" href="/site/index"><img src="/images/new/logo.png" alt=""></a></div><div id="w0-collapse" class="collapse navbar-collapse"><ul id="w1" class="navbar-nav navbar-left nav"><li><a class="change-to-dev" href="javascript:void();" tabindex="0" role="button" data-toggle="popover" data-placement="bottom" data-trigger="focus">触动精灵脚本分发</a></li></ul><ul id="w2" class="navbar-nav navbar-right nav"><li><a href="http://account.touchsprite.com/site/login">登录</a></li>
<li><a href="http://account.touchsprite.com/site/reg">注册</a></li></ul></div></div></nav>        <div class="container main-container min-bodyhei" style="padding-top: 50px; padding-bottom: 70px; box-sizing: border-box; ">
<div class="site-error">

    <div class="page-header">
        <div style="display: inline;">
            <h1>Not Found (#404) <small>Error</small></h1>
        </div>
    </div>

    <div class="alert alert-danger">
        该脚本不存在或您没有权限访问。    </div>

    <p>
        处理您的请求时发生了一些错误。
    </p>
    <p>
        如果您认为并不是自己造成的，请尽快联系我们修复。
        <br>
        反馈 QQ：<a href="http://wpa.qq.com/msgrd?v=3&uin=**********&site=qq&menu=yes">**********</a> 或 
                 <a href="http://wpa.qq.com/msgrd?v=3&uin=**********&site=qq&menu=yes">**********</a>
    </p>

</div>
        </div>
    </div>



    <!-- 开发者协议 -->
    <div class="modal fade" id="developer" tabindex="-1" role="dialog" aria-labelledby="developerLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="developerLabel">开发者协议</h4>
                </div>
                <div class="modal-body">
                    <textarea class="form-control" rows="30"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    

            <footer class="footer elf-footer">
            <div class="container clearfix">
                <!-- <span><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502033362" target="_blank"><img src="http://www.play4u.cn/img/15171127012155dj.png" style="width:14px">京公网安备 11010502033362号</a></span> -->
                <div class="pull-left elf-footer-left"><span>&copy; 2025 触动精灵 | <a href="http://www.miitbeian.gov.cn/" target="_blank">京ICP备15002081号-2</a> <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502033362" target="_blank"><img src="http://www.play4u.cn/img/15171127012155dj.png" style="width:14px">京公网安备 11010502033362号 </a> | <a href="http://www.miitbeian.gov.cn/state/outPortal/loginPortal.action;jsessionid=ceuAGerAFqLJ0fjT6VnYAaAvvGY7dWk4QbGE0Z8txOOzZrim45OC!983800137" target="_blank">增值电信业务经营许可证 京B2-20180754</a></span></div>
                <div class="pull-right elf-footer-right"><a href="http://www.touchsprite.com/about.html?id=tab-1" target="_blank" style="padding-left:0">关于我们</a><a href="http://www.touchsprite.com/contact" target="_blank">联系我们</a><a href="http://www.touchsprite.com/join" target="_blank">加入我们</a><a href="http://www.touchsprite.com/" target="_blank">官方网站</a><a href="http://www.touchsprite.com/proclaim" id="d_action" data-toggle="modal" data-target="#disclaimer">免责声明</a><a href="http://www.touchsprite.com/agreement" target="_blank">用户协议</a><a href="http://account.touchsprite.com/protocol/index" target="_blank">开发者协议</a><script type="text/javascript">var cnzz_protocol = (("https:" == document.location.protocol) ? " https://" : " http://");document.write(unescape("%3Cspan id='cnzz_stat_icon_1274651304'%3E%3C/span%3E%3Cscript src='" + cnzz_protocol + "s22.cnzz.com/z_stat.php%3Fid%3D1274651304' type='text/javascript'%3E%3C/script%3E"));</script><script>$("#d_action").click(function(){$.ajax({url:"/ajax/web?type=disclaimer",type:"GET",dataType:"json",success:function(data){if(data.success){$("#d_content").html(data.content)}}})});</script>
                </div>
            </div>
        </footer>
    
            <!-- 免责声明 -->
    <div class="modal fade" id="disclaimer" tabindex="-1" role="dialog" aria-labelledby="disclaimerLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="disclaimerLabel">免责声明</h4>
                </div>
                <div class="modal-body">
                    <textarea class="form-control" rows="30"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        $('#disclaimer, #developer').on('show.bs.modal', function (e) {  //附件文档，开发者协议
            var modal = $(this);
            $.get('/site/get-footer?type='+modal.attr('id'), function (msg) {
                modal.find('textarea').val(msg);
            })
        })
    </script>
    </body>
</html>
