<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8"/>
    <meta name="apple-mobile-web-app-title" content="触动开发者" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="renderer" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="robots" content="index, follow" />

    <link rel="apple-touch-icon-precomposed" href="/app-icon.png">
    <link rel="shortcut icon" href="/favicon.ico">

    <meta name="csrf-param" content="_csrf">
    <meta name="csrf-token" content="Qk1mWk5GX3cSeiIYBCNqL3sUVBwvAxobCiYjMjQPDU4nBAccJCJpIw==">
    <title>脚本详情</title>
    <meta name="description" content="触动精灵开发者平台" />
    <meta name="HandheldFriendly" content="True" />
    <meta name="MobileOptimized" content="320" />
    <link rel="stylesheet" href="/css/swiper.min.css" />
    <script src="/js/swiper.min.js"></script>
    <link rel="stylesheet" href="//play4u.touchsprite.com/cdn/bootstrap/3.3.5/css/bootstrap.min.css">
    <script src="//play4u.touchsprite.com/cdn/jquery/1.11.3/jquery.min.js"></script>
    <script src="/js/index.js"></script>
    <script src="//play4u.touchsprite.com/cdn/bootstrap/3.3.5/js/bootstrap.min.js"></script>

    <link href="/css/site.css" rel="stylesheet">
<script src="/assets/fd480ddd/yii.js"></script>
    <!--[if lte IE 9]>
    <script src="/js/json2.js"></script>
    <script src="http://apps.bdimg.com/libs/html5shiv/3.7/html5shiv.min.js"></script>
    <script src="http://apps.bdimg.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <!--     <script src="/js/bootstrap-slider.min.js"></script> -->

    <style>
        .wrap > .container {
            padding: 0px 15px 20px;
        }
    </style>

</head>
<body>
<div class="wrap">
    <div class="container main-container min-bodyhei" style="padding-bottom: 70px; box-sizing: border-box; ">
        <div class="preview-view">

    <div class="page-header">
        <div style="display: inline;">
            <h3>火柴人 <small></small></h3>
        </div>
        <h5>平台支持：
                            <span class="label label-primary">iOS</span>
                <span class="label label-success">Android</span>
                    </h5>
        <h5>脚本 ID：88888</h5>
                    <h5>收费类型：付费</h5>
            <h5>试用类型：无试用</h5>
            </div>
    一款极品脚本    <style type="text/css">
        .preview-view *{max-width: 100% !important; word-break: break-all !important;}

    </style>
    <hr />
    <h4>开发者联系方式</h4>
    <table class="table table-striped table-bordered detail-view"><tr><th>QQ</th><td>10086</td></tr></table>    <hr />

    <h5>本脚本由开发者「Comeon红颜」提供</h5>
</div>
    </div>
</div>


<!-- 开发者协议 -->
<div class="modal fade" id="developer" tabindex="-1" role="dialog" aria-labelledby="developerLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="developerLabel">开发者协议</h4>
            </div>
            <div class="modal-body">
                <textarea class="form-control" rows="30"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 免责声明 -->
<div class="modal fade" id="disclaimer" tabindex="-1" role="dialog" aria-labelledby="disclaimerLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="disclaimerLabel">免责声明</h4>
            </div>
            <div class="modal-body">
                <textarea class="form-control" rows="30"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>

<script>
    $('#disclaimer, #developer').on('show.bs.modal', function (e) {  //附件文档，开发者协议
        var modal = $(this);
        $.get('/site/get-footer?type='+modal.attr('id'), function (msg) {
            modal.find('textarea').val(msg);
        })
    })
</script>
</body>
</html>
