#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触动精灵脚本信息爬虫
爬取触动精灵脚本商店的脚本信息并输出为Markdown表格
"""

import requests
from bs4 import BeautifulSoup
import time
import re
import sqlite3
from urllib.parse import urljoin
import logging
from typing import Dict, Optional, List
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tsp_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class TSPScraper:
    def __init__(self, db_path: str = 'tsp_scripts.db'):
        self.base_url = "https://storeauth.touchsprite.com/preview?tsp="
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.init_database()

    def init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建脚本信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scripts (
                script_id INTEGER PRIMARY KEY,
                script_name TEXT,
                platform_support TEXT,
                charge_type TEXT,
                trial_type TEXT,
                script_description TEXT,
                developer_contact TEXT,
                developer_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建爬取进度表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS crawl_progress (
                id INTEGER PRIMARY KEY,
                last_crawled_id INTEGER,
                total_processed INTEGER DEFAULT 0,
                total_success INTEGER DEFAULT 0,
                start_time TIMESTAMP,
                last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()
        logging.info(f"数据库初始化完成: {self.db_path}")

    def get_last_crawled_id(self) -> int:
        """获取上次爬取的最后一个ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT last_crawled_id FROM crawl_progress ORDER BY id DESC LIMIT 1')
        result = cursor.fetchone()

        conn.close()

        if result:
            return result[0]
        return 0

    def update_crawl_progress(self, last_id: int, total_processed: int, total_success: int):
        """更新爬取进度"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 检查是否已有进度记录
        cursor.execute('SELECT id FROM crawl_progress ORDER BY id DESC LIMIT 1')
        existing = cursor.fetchone()

        if existing:
            cursor.execute('''
                UPDATE crawl_progress
                SET last_crawled_id = ?, total_processed = ?, total_success = ?, last_update = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (last_id, total_processed, total_success, existing[0]))
        else:
            cursor.execute('''
                INSERT INTO crawl_progress (last_crawled_id, total_processed, total_success, start_time)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (last_id, total_processed, total_success))

        conn.commit()
        conn.close()

    def save_script_to_db(self, script_info: Dict):
        """保存脚本信息到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO scripts
            (script_id, script_name, platform_support, charge_type, trial_type,
             script_description, developer_contact, developer_name, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            script_info['脚本ID'],
            script_info['脚本名称'],
            script_info['平台支持'],
            script_info['收费类型'],
            script_info['试用类型'],
            script_info['脚本描述'],
            script_info['开发者联系方式'],
            script_info['开发者名称']
        ))

        conn.commit()
        conn.close()

    def get_script_info(self, script_id: int) -> Optional[Dict]:
        """获取单个脚本的信息"""
        url = f"{self.base_url}{script_id}"
        
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            # 检查是否为404页面
            if "Not Found (#404)" in response.text or "该脚本不存在或您没有权限访问" in response.text:
                logging.info(f"脚本ID {script_id}: 不存在或无权限访问")
                return None
                
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查是否有脚本详情页面的标识
            preview_view = soup.find('div', class_='preview-view')
            if not preview_view:
                logging.info(f"脚本ID {script_id}: 页面格式不正确")
                return None
            
            script_info = self.extract_script_info(soup, script_id)
            if script_info:
                # 保存到数据库
                self.save_script_to_db(script_info)
                logging.info(f"成功获取脚本ID {script_id}: {script_info.get('脚本名称', 'N/A')}")

            return script_info
            
        except requests.exceptions.RequestException as e:
            logging.error(f"请求脚本ID {script_id} 失败: {e}")
            return None
        except Exception as e:
            logging.error(f"解析脚本ID {script_id} 失败: {e}")
            return None
    
    def extract_script_info(self, soup: BeautifulSoup, script_id: int) -> Dict:
        """从HTML中提取脚本信息"""
        info = {
            '脚本ID': script_id,
            '脚本名称': 'N/A',
            '平台支持': 'N/A',
            '收费类型': 'N/A',
            '试用类型': 'N/A',
            '脚本描述': 'N/A',
            '开发者联系方式': 'N/A',
            '开发者名称': 'N/A'
        }

        try:
            # 提取脚本名称
            page_header = soup.find('div', class_='page-header')
            if page_header:
                title_h3 = page_header.find('h3')
                if title_h3:
                    script_name = title_h3.get_text().strip()
                    # 移除可能的small标签内容
                    small_tag = title_h3.find('small')
                    if small_tag:
                        script_name = script_name.replace(small_tag.get_text(), '').strip()
                    info['脚本名称'] = script_name

                # 提取平台支持
                platform_labels = page_header.find_all('span', class_='label')
                platforms = []
                for label in platform_labels:
                    platform_text = label.get_text().strip()
                    if platform_text in ['iOS', 'Android']:
                        platforms.append(platform_text)
                info['平台支持'] = ', '.join(platforms) if platforms else 'N/A'

                # 提取收费类型和试用类型
                h5_tags = page_header.find_all('h5')
                for h5 in h5_tags:
                    text = h5.get_text().strip()
                    if text.startswith('收费类型：'):
                        info['收费类型'] = text.replace('收费类型：', '').strip()
                    elif text.startswith('试用类型：'):
                        info['试用类型'] = text.replace('试用类型：', '').strip()

            # 提取脚本描述 - 改进的逻辑
            preview_view = soup.find('div', class_='preview-view')
            if preview_view:
                page_header = preview_view.find('div', class_='page-header')
                if page_header:
                    description_parts = []

                    # 查找page-header后面到第一个hr标签之间的所有内容
                    current = page_header.next_sibling
                    while current:
                        if hasattr(current, 'name'):
                            if current.name == 'hr':
                                break
                            elif current.name == 'style':
                                current = current.next_sibling
                                continue
                            elif current.name == 'p':
                                # 提取p标签的文本内容
                                p_text = current.get_text().strip()
                                if p_text:
                                    description_parts.append(p_text)
                            else:
                                # 其他标签也尝试提取文本
                                tag_text = current.get_text().strip()
                                if tag_text:
                                    description_parts.append(tag_text)
                        elif hasattr(current, 'strip'):
                            # 处理纯文本节点
                            text = current.strip()
                            if text and not text.startswith('<') and not text.startswith('.preview-view'):
                                description_parts.append(text)

                        current = current.next_sibling

                    if description_parts:
                        # 清理描述文本，移除CSS样式等无关内容
                        cleaned_parts = []
                        for part in description_parts:
                            # 跳过CSS样式相关的文本
                            if not any(css_keyword in part.lower() for css_keyword in
                                     ['max-width', 'word-break', 'break-all', '.preview-view', 'important']):
                                cleaned_parts.append(part)

                        if cleaned_parts:
                            info['脚本描述'] = ' '.join(cleaned_parts)

            # 提取开发者联系方式
            contact_table = soup.find('table', class_='table')
            if contact_table:
                contacts = []
                rows = contact_table.find_all('tr')
                for row in rows:
                    th = row.find('th')
                    td = row.find('td')
                    if th and td:
                        contact_type = th.get_text().strip()
                        contact_value = td.get_text().strip()
                        contacts.append(f"{contact_type}: {contact_value}")
                info['开发者联系方式'] = '; '.join(contacts) if contacts else 'N/A'

            # 提取开发者名称 - 改进的逻辑
            # 查找包含"本脚本由开发者"文本的h5标签
            h5_tags = soup.find_all('h5')
            for h5 in h5_tags:
                if h5 and h5.get_text:
                    h5_text = h5.get_text()
                    if '本脚本由开发者' in h5_text:
                        # 使用正则表达式提取开发者名称
                        match = re.search(r'本脚本由开发者「(.+?)」提供', h5_text)
                        if match:
                            info['开发者名称'] = match.group(1)
                        break

        except Exception as e:
            logging.error(f"提取脚本ID {script_id} 信息时出错: {e}")
            import traceback
            logging.error(f"详细错误信息: {traceback.format_exc()}")

        return info
    
    def scrape_range(self, start_id: int, end_id: int, delay: float = 1.0, resume: bool = True):
        """爬取指定范围的脚本信息"""
        # 如果启用断点续传，获取上次爬取的进度
        if resume:
            last_crawled_id = self.get_last_crawled_id()
            if last_crawled_id > start_id:
                start_id = last_crawled_id + 1
                logging.info(f"检测到上次爬取进度，从脚本ID {start_id} 继续爬取")

        logging.info(f"开始爬取脚本ID范围: {start_id} - {end_id}")

        total_processed = 0
        total_success = 0

        for script_id in range(start_id, end_id + 1):
            try:
                script_info = self.get_script_info(script_id)
                total_processed += 1

                if script_info:
                    total_success += 1

                # 添加延迟避免请求过快
                time.sleep(delay)

                # 每100个ID输出一次进度并更新数据库
                if script_id % 100 == 0:
                    self.update_crawl_progress(script_id, total_processed, total_success)
                    logging.info(f"已处理到脚本ID: {script_id}, 已处理: {total_processed} 个, 成功: {total_success} 个")

            except KeyboardInterrupt:
                logging.info("用户中断爬取，保存当前进度...")
                self.update_crawl_progress(script_id - 1, total_processed, total_success)
                break
            except Exception as e:
                logging.error(f"处理脚本ID {script_id} 时出现未知错误: {e}")
                continue

        # 最终更新进度
        self.update_crawl_progress(min(script_id, end_id), total_processed, total_success)
        logging.info(f"爬取完成，共处理 {total_processed} 个脚本，成功收集 {total_success} 个脚本信息")
    
    def export_to_markdown(self, filename: str = 'tsp_scripts.md'):
        """从数据库导出结果为Markdown表格"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT COUNT(*) FROM scripts')
        total_count = cursor.fetchone()[0]

        if total_count == 0:
            logging.warning("数据库中没有数据可导出")
            conn.close()
            return

        cursor.execute('''
            SELECT script_id, script_name, platform_support, charge_type, trial_type,
                   script_description, developer_contact, developer_name
            FROM scripts
            ORDER BY script_id
        ''')

        results = cursor.fetchall()
        conn.close()

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# 触动精灵脚本信息汇总\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"共收集脚本数量: {total_count}\n\n")

            # 写入表格头
            headers = ['脚本ID', '脚本名称', '平台支持', '收费类型', '试用类型', '脚本描述', '开发者联系方式', '开发者名称']
            f.write('| ' + ' | '.join(headers) + ' |\n')
            f.write('| ' + ' | '.join(['---'] * len(headers)) + ' |\n')

            # 写入数据行
            for row in results:
                formatted_row = []
                for value in row:
                    value_str = str(value) if value is not None else 'N/A'
                    # 转义Markdown特殊字符
                    value_str = value_str.replace('|', '\\|').replace('\n', ' ').replace('\r', '')
                    formatted_row.append(value_str)
                f.write('| ' + ' | '.join(formatted_row) + ' |\n')

        logging.info(f"结果已导出到 {filename}")

    def get_statistics(self):
        """获取爬取统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取脚本统计
        cursor.execute('SELECT COUNT(*) FROM scripts')
        total_scripts = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM scripts WHERE charge_type = "免费"')
        free_scripts = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM scripts WHERE charge_type = "付费"')
        paid_scripts = cursor.fetchone()[0]

        # 获取爬取进度
        cursor.execute('SELECT last_crawled_id, total_processed, total_success FROM crawl_progress ORDER BY id DESC LIMIT 1')
        progress = cursor.fetchone()

        conn.close()

        stats = {
            'total_scripts': total_scripts,
            'free_scripts': free_scripts,
            'paid_scripts': paid_scripts,
            'last_crawled_id': progress[0] if progress else 0,
            'total_processed': progress[1] if progress else 0,
            'total_success': progress[2] if progress else 0
        }

        return stats

def main():
    """主函数"""
    scraper = TSPScraper()

    # 显示当前统计信息
    stats = scraper.get_statistics()
    logging.info(f"当前数据库统计: 已收集 {stats['total_scripts']} 个脚本")
    logging.info(f"免费脚本: {stats['free_scripts']}, 付费脚本: {stats['paid_scripts']}")
    logging.info(f"上次爬取到ID: {stats['last_crawled_id']}")

    # 爬取范围：1到231255
    start_id = 1
    end_id = 231255

    # 开始爬取（启用断点续传）
    scraper.scrape_range(start_id, end_id, delay=0.1, resume=True)

    # 显示最终统计
    final_stats = scraper.get_statistics()
    logging.info(f"爬取完成! 最终统计:")
    logging.info(f"数据库中共有 {final_stats['total_scripts']} 个脚本")
    logging.info(f"免费脚本: {final_stats['free_scripts']}, 付费脚本: {final_stats['paid_scripts']}")

    # 可选：导出为Markdown文件
    # scraper.export_to_markdown('tsp_scripts.md')

if __name__ == "__main__":
    main()
