# 触动精灵脚本信息爬虫

这是一个用于收集触动精灵脚本商店脚本信息的Python爬虫工具，支持SQLite数据库存储和断点续传功能。

## 功能特性

- 爬取触动精灵脚本商店中ID为1到231255的所有脚本信息
- 提取以下信息：
  - 脚本ID
  - 脚本名称
  - 平台支持（iOS/Android）
  - 收费类型（免费/付费）
  - 试用类型（有试用/无试用等）
  - 脚本描述
  - 开发者联系方式
  - 开发者名称
- **SQLite数据库存储**：所有数据保存在本地数据库中
- **断点续传**：支持中断后继续爬取，不会重复爬取已有数据
- **进度跟踪**：实时记录爬取进度和统计信息
- 自动处理404页面和无效脚本
- 可导出为Markdown表格格式
- 包含详细的日志记录

## 安装依赖

建议使用conda创建虚拟环境：

```bash
# 创建conda环境
conda create -n tsp_scraper python=3.8
conda activate tsp_scraper

# 安装依赖
pip install -r requirements.txt
```

## 使用方法

### 基本爬取
```bash
# 开始爬取（支持断点续传）
python tsp_scraper.py
```

### 数据库管理
```bash
# 查看统计信息
python tsp_manager.py stats

# 搜索脚本
python tsp_manager.py search "关键词"

# 查看脚本详情
python tsp_manager.py detail 88888

# 导出为Markdown文件
python tsp_manager.py export --output my_scripts.md

# 重置爬取进度（从头开始）
python tsp_manager.py reset
```

## 输出文件

- `tsp_scripts.db`: SQLite数据库文件，包含所有脚本信息
- `tsp_scraper.log`: 详细的运行日志
- `tsp_scripts.md`: 可选的Markdown导出文件

## 注意事项

1. 爬取过程可能需要较长时间（约23万个ID需要检查）
2. 脚本包含1秒延迟以避免对服务器造成过大压力
3. **支持断点续传**：中断后再次运行会自动从上次停止的位置继续
4. 不是所有ID都有对应的脚本，404页面会被自动跳过
5. 数据实时保存到SQLite数据库，即使程序异常退出也不会丢失数据

## 断点续传功能

- 程序会自动记录爬取进度
- 重新运行时会从上次停止的ID继续爬取
- 如需从头开始，使用 `python tsp_manager.py reset` 重置进度
- 每100个ID会自动保存一次进度

## 配置说明

可以在`tsp_scraper.py`中修改以下参数：

- `start_id`: 起始脚本ID（默认1）
- `end_id`: 结束脚本ID（默认231255）
- `delay`: 请求间隔时间（默认1.0秒）
- `db_path`: 数据库文件路径（默认'tsp_scripts.db'）

## 数据库结构

### scripts表
存储脚本信息，包含以下字段：
- script_id (主键)
- script_name
- platform_support
- charge_type
- trial_type
- script_description
- developer_contact
- developer_name
- created_at
- updated_at

### crawl_progress表
存储爬取进度信息：
- last_crawled_id: 最后爬取的ID
- total_processed: 总处理数量
- total_success: 成功收集数量
- start_time: 开始时间
- last_update: 最后更新时间

## 示例输出

生成的Markdown文件包含如下格式的表格：

| 脚本ID | 脚本名称 | 平台支持 | 收费类型 | 试用类型 | 脚本描述 | 开发者联系方式 | 开发者名称 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 88888 | 火柴人 | iOS, Android | 付费 | 无试用 | 一款极品脚本 | QQ: 10086 | Comeon红颜 |
